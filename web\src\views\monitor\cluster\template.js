import i18n from '@/locales/index'
import { formatString, handleChartValuesBykey } from '@/utils/util'
import { formatterFn } from '@/utils/echartsConst'
export const seldomList = {
  uptimeAll: {
    url: '/v1/query_range?query=yashandb_uptime{yasdbName=\'$0$\'}',
    needArgs: 'true'
  }
}
export const handleProcessTyeRes = (data) => {
  const dataMap = new Map()
  const showDeleteIndex = [];
  for (let i = 0, n = data.length; i < n; i++) {
    const metric = data[i]?.metric;
    const key = metric.nodeId + metric.yasdbName + metric?.processName
    if (!dataMap.has(key)) {
      dataMap.set(key, i);
    } else {
      data[dataMap.get(key)]?.values?.push(...data[i].values);
      showDeleteIndex.push(i);
    }
  }
  showDeleteIndex.reverse().map((index) => {
    data.splice(index, 1);
  });
  return data
}
export const urlList = {
  currentSession: {
    url: '/v1/query_range?query=yashandb_current_sessions{yasdbName=\'$0$\',nodeId=~\'$1$\'}',
    needArgs: 'true'
  },
  maxSession: {
    url: '/v1/query_range?query=yashandb_max_sessions{yasdbName=\'$0$\',nodeId=~\'$1$\'}',
    needArgs: 'true'
  },
  systemSession: {
    url: '/v1/query_range?query=yashandb_system_sessions{yasdbName=\'$0$\',nodeId=~\'$1$\'}',
    needArgs: 'true'
  },
  activeUserSession: {
    url: '/v1/query_range?query=yashandb_user_active_sessions{yasdbName=\'$0$\',nodeId=~\'$1$\'}',
    needArgs: 'true'
  },
  inactiveUserSession: {
    url: '/v1/query_range?query=yashandb_user_inactive_sessions{yasdbName=\'$0$\',nodeId=~\'$1$\'}',
    needArgs: 'true'
  },
  systemTablespaceUsage: {
    url: '/v1/query_range?query=yashandb_tablespace_used_percentage{yasdbName=\'$0$\',nodeId=~\'$1$\', name=~\'$2$\'}',
    needArgs: 'true'
  },
  userTablespcaceNames: {
    url: '/v1/query_range?query=yashandb_tablespace_used_percentage{yasdbName=\'$0$\',nodeId=~\'$1$\'}',
    needArgs: 'true'
  },
  undoTablespaceUsage: {
    url: '/v1/query_range?query=((yashandb_dba_tablespace_total_bytes - (yashandb_dba_tablespace_user_bytes%2Byashandb_dba_tablespace_block_size*(yashandb_undo_segments_ublk_count_total%2Byashandb_undo_segments_ufb_count_total)))/yashandb_dba_tablespace_max_size{yasdbName=\'$0$\',nodeId=~\'$1$\',name=~\'$2$\'})*100',
    needArgs: 'true'
  },
  diskPhysicalRead: {
    url: '/v1/query_range?query=yashandb_disk_reads{yasdbName=\'$0$\',nodeId=~\'$1$\'}',
    needArgs: 'true'
  },
  diskReadTime: {
    url: '/v1/query_range?query=yashandb_disk_read_time{yasdbName=\'$0$\',nodeId=~\'$1$\'}',
    needArgs: 'true'
  },
  hitRatio: {
    url: '/v1/query_range?query=yashandb_cache_hit_ratio{yasdbName=\'$0$\',nodeId=~\'$1$\'}',
    needArgs: 'true'
  },
  bufferGets: {
    url: '/v1/query_range?query=yashandb_buffer_gets{yasdbName=\'$0$\',nodeId=~\'$1$\'}',
    needArgs: 'true'
  },
  waitsOther: {
    url: '/v1/query_range?query=sum by(yasdbName, nodeId)(yashandb_count_waits{yasdbName=\'$0$\',nodeId=~\'$1$\'})',
    needArgs: 'true'
  },
  nodeCpuUsage: {
    url: '/v1/query_range?query=node_monit_cpu_uasge{yasdbName=\'$0$\',nodeId=~\'$1$\'}',
    needArgs: 'true'
  },
  nodeMemUsage: {
    url: '/v1/query_range?query=node_monit_mem_uasge{yasdbName=\'$0$\',nodeId=~\'$1$\'}',
    needArgs: 'true'
  },
  nodeMemTotal: {
    url: '/v1/query_range?query=node_monit_mem_total{yasdbName=\'$0$\',nodeId=~\'$1$\'}/1024/1024',
    needArgs: 'true'
  },
  nodeFileOpen: {
    url: '/v1/query_range?query=node_monit_file_open{yasdbName=\'$0$\',nodeId=~\'$1$\'}',
    needArgs: 'true'
  },
  ops: {
    url: '/v1/query_range?query=irate(yashandb_operations{yasdbName=\'$0$\',nodeId=~\'$1$\'}[1m])',
    needArgs: 'true'
  },
  qps: {
    url: '/v1/query_range?query=irate(yashandb_querys{yasdbName=\'$0$\',nodeId=~\'$1$\'}[1m])',
    needArgs: 'true'
  },
  tps: {
    url: '/v1/query_range?query=irate(yashandb_transactions{yasdbName=\'$0$\',nodeId=~\'$1$\'}[1m])',
    needArgs: 'true'
  },
  sqlAvgResponse: {
    url: '/v1/query_range?query=yashandb_avg_elapsed_time_sec{yasdbName=\'$0$\',nodeId=~\'$1$\'}',
    needArgs: 'true'
  },
  highFrequencySql: {
    url: '/v1/query_range?query=yashandb_high_frequencies_sql_count{yasdbName=\'$0$\',nodeId=~\'$1$\'}',
    needArgs: 'true'
  },
  connectCpuUsed: {
    url: '/v1/query_range?query=count(increase(yashandb_high_frequencies_sql{yasdbName=\'$0$\',nodeId=~\'$1$\'}[1h])>10000 OR COUNT(increase(yashandb_high_frequencies_sql{yasdbName=\'$0$\',nodeId=~\'$1$\'}[1h])>-1)by(yasdbName,nodeId))by(yasdbName,nodeId)-1',
    needArgs: 'true'
  },
  connectTPS: {
    url: '/v1/query_range?query=count(increase(yashandb_high_frequencies_sql{yasdbName=\'$0$\',nodeId=~\'$1$\'}[1h])>10000 OR COUNT(increase(yashandb_high_frequencies_sql{yasdbName=\'$0$\',nodeId=~\'$1$\'}[1h])>-1)by(yasdbName,nodeId))by(yasdbName,nodeId)-1',
    needArgs: 'true'
  },
  connectDelay: {
    url: '/v1/query_range?query=count(increase(yashandb_high_frequencies_sql{yasdbName=\'$0$\',nodeId=~\'$1$\'}[1h])>10000 OR COUNT(increase(yashandb_high_frequencies_sql{yasdbName=\'$0$\',nodeId=~\'$1$\'}[1h])>-1)by(yasdbName,nodeId))by(yasdbName,nodeId)-1',
    needArgs: 'true'
  },
  syncDelay: {
    url: '/v1/query_range?query=yashandb_sync_delay{yasdbName=\'$0$\',nodeId=~\'$1$\'}',
    needArgs: 'true'
  },
  longTransactions: {
    url: '/v1/query_range?query=yashandb_long_transactions{yasdbName=\'$0$\',nodeId=~\'$1$\'}',
    needArgs: 'true'
  },
  spinlockTimes: {
    url: '/v1/query_range?query=yashandb_spinlock_times{yasdbName=\'$0$\',nodeId=~\'$1$\', name=~\'$2$\'}',
    needArgs: 'true'
  },
  spinlockIncrease: {
    url: '/v1/query_range?query=increase(yashandb_spinlock_times{yasdbName=\'$0$\',nodeId=~\'$1$\', name=~\'$2$\'}[1m])',
    needArgs: 'true'
  },
  tablespaceOccupy: {
    url: '/v1/query_range?query=yashandb_sum_tablespaces{yasdbName=\'$0$\',nodeId=~\'$1$\'}',
  },
  // 数据库进程
  processCpuUse: {
    url: '/v1/query_range?query=(sum(increase(yasprocess_cpu_seconds_total{yasdbName=\'$0$\',nodeId=\'$1$\'}[1m]))by(instance, processName, yasdbName))/(sum(increase(yasprocess_node_cpu_seconds_total{yasdbName=\'$0$\',nodeId=\'$1$\'}[1m]))by(instance, processName, yasdbName))*100'
  },
  processMemUse: {
    url: '/v1/query_range?query=yasprocess_mem_usage{yasdbName=\'$0$\', nodeId=\'$1$\'}'
  },
  processActive: {
    url: '/v1/query_range?query=yasprocess_up{yasdbName=\'$0$\', nodeId=\'$1$\'}'
  },
  currentOpenFile: {
    url: '/v1/query_range?query=yasprocess_cur_open_files{yasdbName=\'$0$\',nodeId=\'$1$\'}'
  },
  userSessionType: {
    url: '/v1/query_range?query=yashandb_user_session_driver_count{yasdbName=\'$0$\',nodeId=\'$1$\'}',
  },
  archiveFileSize: {
    url: '/v1/query_range?query=yasprocess_threads_count{yasdbName=\'$0$\',nodeId=\'$1$\'}'
  },
  yashandbProcessThreadsCount: {
    url: '/v1/query_range?query=yasprocess_threads_count{yasdbName=\'$0$\',nodeId=\'$1$\'}'
  },
  
}

// 数据库连接数
const sessionChart = {
  id: 1,
  title: i18n.t('cluster.detail.chart.session'),
  display: {
    line: true
  },
  axis: {
    left: {
      show: true,
      unit: 'item'
    }
  },
  urls: [
    { path: 'currentSession', key: i18n.t('cluster.detail.chart.currentSession') },
    { path: 'systemSession', key: i18n.t('cluster.detail.chart.systemSession') },
    { path: 'activeUserSession', key: i18n.t('cluster.detail.chart.userSession.active') },
    { path: 'inactiveUserSession', key: i18n.t('cluster.detail.chart.userSession.inactive') }
  ],
  data: []
}
// 系统表空间使用率
const tableUsedChart = {
  id: 2,
  title: i18n.t('cluster.detail.chart.tablespace'),
  display: {
    line: true
  },
  axis: {
    left: {
      show: true,
      unit: '%'
    }
  },
  showSelect: true,
  selected: null,
  key: 'systemTablespaceUsage',
  urls: [
    {
      path: 'systemTablespaceUsage',
      paramsMerge: true, // url上的name 参数合并，降低请求次数
      legendName: {}, // 图例key根据name命名
      hanldeCustomResult: (data, info) => {
        return handleChartValuesBykey(data, (metric) => metric.name)
      },
      hanlderCustomParams: (targetUrl, info) => {
        let name = ''
        if (info.selected === i18n.t('all')) {
          name = info.urls[0].allUrl
        } else if (info.selected.includes('UNDO')) {
          name = ''
        } else {
          name = info.selected
        }
        return formatString(targetUrl, ...Object.values({
          ...info.extraParam,
          name
        }))
      },
    },
    {
      path: 'undoTablespaceUsage',
      legendName: {},
      paramsMerge: true,
      hanlderCustomParams: (targetUrl, info) => {
        let name = ''
        const undoName = Object.keys(info.urls[1].legendName).join('|')
        if (info.selected === i18n.t('all')) {
          name = undoName
        } else if (info.selected.includes('UNDO')) {
          name = info.selected
        } else {
          name = ''
        }
        return formatString(targetUrl, ...Object.values({
          ...info.extraParam,
          name
        }))
      }
    },
  ],
  data: []
}

// 用户表空间使用率
const userTableUsedChart = {
  id: 28,
  title: i18n.t('cluster.detail.chart.user.tablespace'),
  display: {
    line: true
  },
  axis: {
    left: {
      show: true,
      unit: '%'
    }
  },
  showSelect: true,
  selected: null,
  key: 'userTablespcaceNames',
  costomOptiosn: {
    tooltip: {
      enterable: true,
      confine: true,
      extraCssText: 'max-width: 800px;max-height: 500px;overflow-y: auto;box-shadow: 0px 10px 32px 0px rgba(8, 31, 100, 0.06),0px 5px 12px 0px rgba(8, 31, 100, 0.1);'
    },
  },
  urls: [
    {
      path: 'userTablespcaceNames',
      key: 'userTablespcaceNames',
      paramsMerge: true,
      hanldeCustomResult: (data, info) => {
        // 过滤已经被用户删除的图表数据
        const filterData = data.filter(item => {
          const legendName = info.urls[0].legendName
          return legendName[item?.metric.name]
        })
        return info.selected === i18n.t('all') ? handleChartValuesBykey(filterData, (metric) => metric.name) : handleChartValuesBykey(data, (metric) => metric.name)
      },
      legendName: {},
      hanlderCustomParams: (targetUrl, info) => {
        if (info.selected === i18n.t('all')) {
          const allUrl = info.urls[0].allUrl
          return formatString(allUrl, ...Object.values(info.extraParam))
        } else {
          const url = urlList['userTablespcaceNames'].url.replace('}', `,name=~'${info.selected}'}`)
          return formatString(url, ...Object.values(info.extraParam))
        }
      }
    }
  ],
  data: []
}

// 磁盘读取次数
const diskReadNumChart = {
  id: 3,
  title: i18n.t('cluster.detail.chart.disk.physical.read'),
  display: {
    line: true
  },
  axis: {
    left: {
      show: true,
      unit: 'piece'
    }
  },
  urls: [
    { path: 'diskPhysicalRead', key: i18n.t('cluster.detail.chart.disk.physical.read') }
  ],
  data: []
}
// 磁盘读取时长
const diskReadTimeChart = {
  id: 4,
  title: i18n.t('cluster.detail.chart.disk.read'),
  display: {
    line: true
  },
  axis: {
    left: {
      show: true,
      unit: 'ms'
    }
  },
  urls: [
    { path: 'diskReadTime', key: i18n.t('cluster.detail.chart.disk.read') }
  ],
  data: []
}
// 缓存命中率
const hitRatioChart = {
  id: 5,
  title: i18n.t('cluster.detail.chart.hitRatio'),
  display: {
    line: true,
    // area: true 不再展示区域a
  },
  axis: {
    left: {
      show: true,
      unit: '%'
    }
  },
  urls: [
    { path: 'hitRatio', key: i18n.t('cluster.detail.chart.hitRatio') }
  ],
  data: []
}
// 内存读取次数
const memoryReadTimeChart = {
  id: 6,
  title: i18n.t('cluster.detail.chart.bufferGets'),
  display: {
    brokeLine: true
  },
  axis: {
    left: {
      show: true,
      unit: 'piece'
    }
  },
  urls: [
    { path: 'bufferGets', key: i18n.t('cluster.detail.chart.bufferGets') }
  ],
  data: []
}
// 等待事件数量
const waitsOtherChart = {
  id: 7,
  title: i18n.t('cluster.detail.chart.waitsOther'),
  display: {
    line: true
  },
  axis: {
    left: {
      show: true,
      unit: 'item'
    }
  },
  urls: [
    { path: 'waitsOther', key: i18n.t('cluster.detail.chart.waitsOther') }
  ],
  data: [],
  costomOptiosn: {
    tooltip: {
      enterable: true,
      confine: true,
      extraCssText: 'max-width: 800px;max-height: 500px;overflow-y: auto;box-shadow: 0px 10px 32px 0px rgba(8, 31, 100, 0.06),0px 5px 12px 0px rgba(8, 31, 100, 0.1);'
    },
  },
  eventsUrl: '/v1/query_range?query=sum by(yasdbName, nodeId, name)(yashandb_count_waits{yasdbName=\'$0$\',nodeId=~\'$1$\', name!="null"})',
  hanlderCustomTooltip: function ({ params, eventsData }) {
    const time = params[0].axisValue / 1000
    const filterData = eventsData.map(item => {
      return {
        name: item.metric.name,
        value: item.values.find(e => e[0] === time)?.[1] || 0
      }
    })
    let htmlStr = ''
    const nameStr = `<span style="margin-right: 10px;">${i18n.t('cluster.detail.chart.waitsOther')}</span>`
    const numSrt = params[0].data[1] ? params[0].data[1] + i18n.t('item') : '-'
    htmlStr += '<div style="margin-bottom:12px;"> ' + nameStr + numSrt + '<div>'
    filterData.forEach(param => {
      htmlStr += `<div style="position:relative;margin-top:5px; display: ${param.value ? 'block' : 'none'};">`
      htmlStr += '  <div style="display:inline-block;margin-left:6px;"></div>'
      htmlStr += '  <div style="position:absolute;top:50%;margin-left:-5px;margin-top: -5px;width: 8px;height: 8px;border-radius: 50%;background: #027AFF;"></div>'
      htmlStr += `<span>`
      const showDataText = param.value ? formatterFn('item')(param.value) : '-'
      htmlStr += param.name + '</span>' + '<span style="float:right;margin:0 0 0 20px;">' + showDataText + '</span>' + '</div>'
    })
    return htmlStr
  }
}
// 进程CPU利用率
// const cpuUsageChart = {
//   id: 8,
//   title: i18n.t('cluster.detail.chart.node.cpuUsage'),
//   display: {
//     line: true
//   },
//   axis: {
//     left: {
//       show: true,
//       unit: '%'
//     }
//   },
//   urls: [
//     { path: 'nodeCpuUsage', key: i18n.t('cluster.detail.chart.node.cpuUsage') }
//   ],
//   data: []
// }
// 进程内存利用率
// const memoryUsedChart = {
//   id: 9,
//   title: i18n.t('cluster.detail.chart.node.memUsage'),
//   display: {
//     line: true
//   },
//   axis: {
//     left: {
//       show: true,
//       unit: '%'
//     }
//   },
//   urls: [
//     { path: 'nodeMemUsage', key: i18n.t('cluster.detail.chart.node.memUsage') }
//   ],
//   data: []
// }
// 进程内存使用
// const nodeMemTotalChart = {
//   id: 10,
//   title: i18n.t('cluster.detail.chart.node.memTotal'),
//   display: {
//     line: true
//   },
//   axis: {
//     left: {
//       show: true,
//       unit: 'MB'
//     }
//   },
//   urls: [
//     { path: 'nodeMemTotal', key: i18n.t('cluster.detail.chart.node.memTotal') }
//   ],
//   data: []
// }
// 进程打开文件数
// const nodeFileOpen = {
//   id: 11,
//   title: i18n.t('cluster.detail.chart.node.fileOpen'),
//   display: {
//     line: true
//   },
//   axis: {
//     left: {
//       show: true,
//       unit: 'item'
//     }
//   },
//   urls: [
//     { path: 'nodeFileOpen', key: i18n.t('cluster.detail.chart.node.fileOpen') }
//   ],
//   data: []
// }
// 数据库每秒的操作数
const opsChart = {
  id: 12,
  title: i18n.t('cluster.detail.chart.ops'),
  display: {
    brokeLine: true
  },
  axis: {
    left: {
      show: true,
      unit: 'piece'
    }
  },
  urls: [
    { path: 'ops', key: i18n.t('cluster.detail.chart.ops') }
  ],
  data: []
}
// 数据库每秒的查询数
const qpsChart = {
  id: 13,
  title: 'QPS',
  display: {
    brokeLine: true
  },
  axis: {
    left: {
      show: true,
      unit: 'piece'
    }
  },
  urls: [
    { path: 'qps', key: 'QPS' }
  ],
  data: []
}
// 数据库每秒的事务数
const tpsChart = {
  id: 14,
  title: 'TPS',
  display: {
    brokeLine: true
  },
  axis: {
    left: {
      show: true,
      unit: 'piece'
    }
  },
  urls: [
    { path: 'tps', key: 'TPS' }
  ],
  data: []
}
// SQL平均响应时间
const sqlAvgResponseChart = {
  id: 15,
  title: i18n.t('cluster.detail.chart.sql.avg.response'),
  display: {
    line: true
  },
  axis: {
    left: {
      show: true,
      unit: 's'
    }
  },
  notFixed: true,
  urls: [
    {
      path: 'sqlAvgResponse',
      key: i18n.t('cluster.detail.chart.sql.avg.response'),
    }
  ],
  data: [],
}
// 高频SQL
const highFrequencySqlChart = {
  id: 16,
  title: i18n.t('cluster.detail.chart.high.fcy.sql'),
  display: {
    line: true
  },
  axis: {
    left: {
      show: true,
      unit: 'item'
    }
  },
  urls: [
    { path: 'highFrequencySql', key: i18n.t('cluster.detail.chart.high.fcy.sql') }
  ],
  data: [],
  xxlSpan: 24
}
// CPU使用率/数据库连接数
const connectCpuUsedChart = {
  id: 17,
  custom: true,
  title: i18n.t('alarm.connect.cpu.used'),
  display: {
    line: true
  },
  axis: {
    left: {
      show: true,
      unit: '%'
    }
  },
  urls: [
    { path: 'connectCpuUsed', key: i18n.t('host.overview.all.cpu'), dataStructIndex: 'cpuUsagePercent' }
  ],
  data: []
}
// TPS/数据库连接数
const connectTPSChart = {
  id: 18,
  custom: true,
  title: i18n.t('alarm.connect.tps'),
  display: {
    line: true
  },
  axis: {
    left: {
      show: true,
      unit: 'piece'
    }
  },
  urls: [
    { path: 'connectTPS', key: i18n.t('cluster.detail.chart.tps'), dataStructIndex: 'tps' }
  ],
  data: []
}
// 时延/数据库连接数
const connectDelayChart = {
  id: 19,
  custom: true,
  title: i18n.t('alarm.connect.delay'),
  display: {
    line: true
  },
  axis: {
    left: {
      show: true,
      unit: 's'
    }
  },
  notFixed: true,
  urls: [
    { path: 'connectDelay', key: i18n.t('cluster.network.ping.rtt'), dataStructIndex: 'averageSQLCostSeconds' }
  ],
  data: []
}

/**
 * 新增监控图表：数据库主备节点同步延迟（单位:s）,长事务（单位：个）-监控超过三分钟未回滚或未提交的事务个数
 * @time 2023-10-23
 * @backendDeveloper 刘顺鹏
 */
export const syncDelayChart = {
  id: 20,
  title: i18n.t('cluster.detail.chart.syncDelay'),
  desc: i18n.t('cluster.detail.chart.syncDelay.tip'),
  display: {
    line: true
  },
  axis: {
    left: {
      show: true,
      unit: 's'
    }
  },
  urls: [
    { path: 'syncDelay', key: i18n.t('cluster.detail.chart.syncDelay') }
  ],
  data: []
}

const longTransactionsChart = {
  id: 21,
  title: i18n.t('cluster.detail.chart.longTransactions'),
  desc: i18n.t('cluster.detail.chart.longTransactions.tip'),
  display: {
    line: true
  },
  axis: {
    left: {
      show: true,
      unit: 'piece'
    }
  },
  urls: [
    { path: 'longTransactions', key: i18n.t('cluster.detail.chart.longTransactions') }
  ],
  data: []
}

// spinlock等待次数
const spinlockTimesChart = {
  id: 22,
  title: i18n.t('cluster.spinlock.time'),
  display: {
    line: true
  },
  axis: {
    left: {
      show: true,
      unit: 'piece'
    }
  },
  key: 'spinlockTimes',
  urls: [
    {
      path: 'spinlockTimes',
      key: 'spinlockTimes',
      paramsMerge: true,
      legendName: {},
      hanldeCustomResult: (data) => handleChartValuesBykey(data, ((metric) => metric?.nodeId + metric?.yasdbName + metric?.name))
    }
  ],
  data: []
}

// spinlock等待次数增量
const spinlockIncreaseChart = {
  id: 23,
  title: i18n.t('cluster.spinlock.increase'),
  key: 'spinlockIncrease',
  display: {
    line: true
  },
  axis: {
    left: {
      show: true,
      unit: 'piece'
    }
  },
  urls: [
    {
      path: 'spinlockIncrease',
      key: 'spinlockIncrease',
      paramsMerge: true,
      legendName: {},
      hanldeCustomResult: (data) => handleChartValuesBykey(data, ((metric) => metric?.nodeId + metric?.yasdbName + metric?.name))
    }
  ],
  data: []
}

// 进程CPU使用率
const processCpuUsageRateChart = {
  id: 24,
  title: i18n.t('host.overview.process.cpu'),
  size: 'small',
  display: {
    line: true,
    // area: true
  },
  axis: {
    left: {
      show: true,
      unit: '%'
    }
  },
  urls: [
    {
      path: 'processCpuUse',
      key: 'processCpuUse',
      alias: i18n.t('host.overview.process.cpu'),
      hanldeCustomResult: (data) => handleChartValuesBykey(data, ((metric) => metric?.nodeId + metric?.yasdbName + metric?.processName))
    }],
  data: []
}
// 进程内存使用率
const processMemoryUsageRateChart = {
  id: 25,
  title: i18n.t('host.overview.process.mem'),
  size: 'small',
  display: {
    line: true
  },
  axis: {
    left: {
      show: true,
      unit: '%'
    }
  },
  urls: [
    {
      path: 'processMemUse',
      key: 'processMemUse',
      alias: i18n.t('host.overview.process.mem'),
      hanldeCustomResult: (data) => handleChartValuesBykey(data, (metric) => metric.nodeId + metric.yasdbName + metric?.processName)
    }
  ],
  data: []
}
// 进程存活状态
const processActiveChart = {
  id: 26,
  title: i18n.t('host.overview.process.active'),
  size: 'small',
  display: {
    line: true
  },
  axis: {
    left: {
      show: true,
    }
  },
  urls: [
    {
      path: 'processActive',
      key: i18n.t('host.overview.process.active'),
      hanldeCustomResult: (data) => handleChartValuesBykey(data, (metric) => metric.nodeId + metric.yasdbName + metric?.processName)
    }
  ],
  data: [],
  extendOptions: {
    extrText: i18n.t('host.overview.process.active.extr')
  }
}

// 表空间占用大小
const tablespaceUseChart = {
  id: 27,
  title: i18n.t('cluster.tablespace.occupy.size'),
  size: 'small',
  display: {
    line: true
  },
  axis: {
    left: {
      show: true,
      unit: 'MB'
    }
  },
  urls: [{ path: 'tablespaceOccupy', key: i18n.t('cluster.tablespace.occupy.size') }],
  data: [],
}

// 进程文件描述符数量
const currentOpenFileChart = {
  id: 29,
  title: i18n.t('cluster.detail.chart.current.open.file'),
  display: {
    line: true
  },
  axis: {
    left: {
      show: true,
      unit: 'item'
    }
  },
  urls: [
    { path: 'currentOpenFile', key: i18n.t('cluster.detail.chart.current.open.file'), hanldeCustomResult: (data) => handleProcessTyeRes(data) }
  ],
}

// 用户会话连接类型数
const userSessionTypeChart = {
  id: 30,
  title: i18n.t('cluster.detail.chart.user.session.type'),
  display: {
    line: true
  },
  axis: {
    left: {
      show: true,
      unit: 'item'
    }
  },
  urls: [
    { 
      path: 'userSessionType', 
      key: i18n.t('cluster.detail.chart.user.session.type'),
      legendName: {},
      customKey: 'client_driver',
      hanldeCustomResult: (data) => handleChartValuesBykey(data, (metric) => metric?.client_driver) 
    }
  ],
  data: []
}

// 归档文件大小
const archiveFileSizeChart = {
  id: 31,
  title: i18n.t('cluster.detail.chart.archive.file.size'),
  display: {
    line: true
  },
  axis: {
    left: {
      show: true,
      unit: 'MB'
    }
  },
  urls: [
    { path: 'archiveFileSize', key: i18n.t('cluster.detail.chart.archive.file.size') }
  ],
  data: []
}

// 数据库进程线程数
const yashandbProcessThreadsCount = {
  id: 32,
  title: i18n.t('monitor.tab.yashandb.process.thread.num'),
  display: {
    line: true
  },
  axis: {
    left: {
      show: true,
      unit: 'item'
    }
  },
  urls: [
    { path: 'yashandbProcessThreadsCount', key: i18n.t('monitor.tab.yashandb.process.thread.num'), hanldeCustomResult: (data) => handleProcessTyeRes(data) }
  ],
  data: []
}

// 生成GV图表
const stat_name = [
  { name: 'GC CR BLOCK RECEIVE TIME', unit: 'μs'},
  { name: 'GC CR BLOCK RECEIVED', unit: 'item' },
  { name: 'GC CR BLOCK RETRIES', unit: '' },
  { name: 'GC CR BLOCK SERVED', unit: 'item' },
  { name: 'GC REMOTE CR GRANT TIME', unit: 'μs' },
  { name: 'GC REMOTE CR GRANTS', unit: '' },
]

export const generateGv = () => {
  let id = 33
  const charts = stat_name.map((item, index) => {
    urlList[item.name] = {
      url: `/v1/query_range?query=yashandb_sysstat_value{yasdbName=\'$0$\',nodeId=~\'$1$\', stat_name='${item.name}'}`,
      needArgs: 'true'
    }
    return {
      id: id + index,
      title: item.name,
      display: {
        line: true
      },
      axis: {
        left: {
          show: true,
          unit: item.unit
        }
      },
      urls: [
        { path: item.name, key: item.name }
      ],
      data: []
    }
  })
  return charts
} 


const chartTemplate = [
  opsChart,
  qpsChart,
  tpsChart,
  sessionChart,
  tablespaceUseChart,
  tableUsedChart,
  userTableUsedChart,
  diskReadNumChart,
  diskReadTimeChart,
  hitRatioChart,
  memoryReadTimeChart,
  waitsOtherChart,
  // cpuUsageChart,
  // memoryUsedChart,
  // nodeMemTotalChart,
  // nodeFileOpen,
  sqlAvgResponseChart,
  highFrequencySqlChart,
  connectCpuUsedChart,
  connectTPSChart,
  connectDelayChart,
  syncDelayChart,
  longTransactionsChart,
  spinlockTimesChart,
  spinlockIncreaseChart,
  processCpuUsageRateChart,
  processMemoryUsageRateChart,
  processActiveChart,
  currentOpenFileChart,
]

export const yashanDeChartsTemplate = {
  charts: [...chartTemplate],
  urls: urlList
}

export const yashanSeChartsTemplate = {
  charts: [...chartTemplate],
  urls: urlList
}

export const yashanCeChartsTemplate = {
  charts: [...chartTemplate],
  urls: urlList
}

export {
  opsChart,
  qpsChart,
  tpsChart,
  sessionChart,
  tableUsedChart,
  userTableUsedChart,
  diskReadNumChart,
  diskReadTimeChart,
  hitRatioChart,
  memoryReadTimeChart,
  waitsOtherChart,
  sqlAvgResponseChart,
  archiveFileSizeChart,
  userSessionTypeChart
}

export const clusterChartsCategory = {
  performance: [
    qpsChart,
    tpsChart,
    opsChart,
    sessionChart,
    waitsOtherChart,
    syncDelayChart,
    longTransactionsChart,
    spinlockTimesChart,
    spinlockIncreaseChart,
    sqlAvgResponseChart,
  ],
  sql: [
    highFrequencySqlChart
    // top 10 慢sql表格
    // top sql 详情页面
  ],
  storage: [
    tablespaceUseChart,
    tableUsedChart,
    userTableUsedChart,
    diskReadNumChart,
    diskReadTimeChart,
    hitRatioChart,
    memoryReadTimeChart,
    archiveFileSizeChart
  ],
  process: [
    processCpuUsageRateChart,
    processMemoryUsageRateChart,
    processActiveChart,
    currentOpenFileChart,
    userSessionTypeChart,
    yashandbProcessThreadsCount,
  ]
}