import moment from 'moment'
import i18n from '@/locales'

export function getEchartOption (){
  return {
    series: [
    ],
    legend: {
      type: 'scroll',
      x: 'center',
      y: 'bottom',
      height: 30,
      icon: 'path://M0 448m0 0l1024 0q0 0 0 0l0 128q0 0 0 0l-1024 0q0 0 0 0l0-128q0 0 0 0Z'
    },
    grid: {
      top: 12,
      right: 16,
      bottom: 30,
      left: 4,
      containLabel: true // 确保y轴刻度存在
    },
    color: ['#027AFF', '#07B9B9', '#FF8F1F', '#722ED1', '#8543e0'],
    tooltip: {
      trigger: 'axis', // 最近的x轴点出现tooltip
      appendToBody: true,
      backgroundColor: '#263240',
      borderColor: '#263240',
      padding: 12,
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      extraCssText: 'box-shadow: 0px 10px 32px 0px rgba(8, 31, 100, 0.06),0px 5px 12px 0px rgba(8, 31, 100, 0.1);'
    },
    axisPointer: {
      lineStyle: {
        type: 'solid' // 坐标轴指示线是直线
      }
    },
    xAxis: {
      type: 'time',
      splitNumber: 5,
      axisLine: {
        lineStyle: {
          color: '#e7e7e7'
        }
      },
      axisLabel: {
        color: '#333'
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {},
      splitNumber: 4,
      min: 'dataMin'
    },
    animation: false // 关闭动画
  }
}

export function getCategoryEchartOption (){
  return {
    series: [
    ],
    legend: {
      type: 'scroll',
      x: 'center',
      y: 'bottom',
      height: 30,
      icon: 'path://M0 448m0 0l1024 0q0 0 0 0l0 128q0 0 0 0l-1024 0q0 0 0 0l0-128q0 0 0 0Z'
    },
    grid: {
      top: 12,
      right: 16,
      bottom: 30,
      left: 4,
      containLabel: true // 确保y轴刻度存在
    },
    color: ['#027AFF', '#07B9B9', '#FF8F1F', '#722ED1', '#8543e0'],
    tooltip: {
      trigger: 'axis', // 最近的x轴点出现tooltip
      appendToBody: true,
      backgroundColor: '#263240',
      borderColor: '#263240',
      padding: 12,
      textStyle: {
        color: '#fff',
        fontSize: 12
      },
      extraCssText: 'box-shadow: 0px 10px 32px 0px rgba(8, 31, 100, 0.06),0px 5px 12px 0px rgba(8, 31, 100, 0.1);'
    },
    axisPointer: {
      lineStyle: {
        type: 'solid' // 坐标轴指示线是直线
      }
    },
    xAxis: {
      type: 'category',
      splitNumber: 5,
      axisLine: {
        lineStyle: {
          color: '#e7e7e7'
        }
      },
      axisLabel: {
        color: '#333'
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {},
      splitNumber: 4,
      min: 'dataMin'
    },
    animation: false // 关闭动画
  }
}

function byteTransfer (value, isUpper = false) {
  if (value === '-') return value
  var unitNum = 1
  var kUnitNum = 1024 * unitNum
  var mUnitNum = 1024 * kUnitNum
  var gUnitNum = 1024 * mUnitNum
  var tUnitNum = 1024 * gUnitNum
  if (value < kUnitNum) {
    return parseFloat((+value).toFixed(2)) + (isUpper ? 'B' : 'b')
  } else if (value >= kUnitNum && value < mUnitNum) {
    return parseFloat((value / kUnitNum).toFixed(2)) + (isUpper ? 'KB' : 'kb')
  } else if (value >= mUnitNum && value < gUnitNum) {
    return parseFloat((value / mUnitNum).toFixed(2)) + (isUpper ? 'MB' : 'mb')
  } else if (value >= gUnitNum && value < tUnitNum) {
    return parseFloat((value / gUnitNum).toFixed(2)) + (isUpper ? 'GB' : 'gb')
  } else if (value >= tUnitNum) {
    return parseFloat((value / tUnitNum).toFixed(2)) + (isUpper ? 'TB' : 'tb')
  }
}

export function getUnitText (unit){
  switch (unit) {
  case '%':
    return '%'
  case 'Bps':
    return 'Bps'
  case 'bps':
    return 'bps'  
  case 'MB':
    return 'MB'
  case 'μs':
    return 'μs'  
  case 'ms':
    return 'ms'
  case 's':
    return 's'
  case 'item':
    return i18n.t('monitor.unit.time.ge')
  case 'IOPS':
    return 'IOPS'
  case 'piece':
    return i18n.t('monitor.unit.time')
  default:
    return i18n.t('monitor.unit.time')
  }
}

// 网络图吞流量单位转换
export const NETWORK_UNIT = ['bps', 'Kbps', 'Mbps', 'Gbps', 'Tbps']

export const transferNetworkUnit = (value) => {
  if (value === '-') return value
  let index = 0
  while (value >= 1024 && index < 4) {
    value = value / 1024
    index++
  }
  return parseFloat(value.toFixed(2)) + NETWORK_UNIT[index]
}

// 时间单位转换
export const transferTimeUnit = (value, unit) => {
  if (!value || value === '-') return value
  const units = ['μs', 'ms', 's', 'm', 'h', 'd'];
  const factors = {
    'μs': 1,          // 微秒作为最小单位
    'ms': 1000,       // 1毫秒 = 1000微秒
    's': 1000 * 1000, // 1秒 = 1000毫秒 = 1000000微秒
    'm': 60 * 1000 * 1000, // 1分钟 = 60秒
    'h': 60 * 60 * 1000 * 1000, // 1小时 = 60分钟
    'd': 24 * 60 * 60 * 1000 * 1000, // 1天 = 24小时
  }
  // 将值转换为微秒（最小单位）
  let valueInBaseUnit = value * factors[unit];
  let currentUnitIndex = units.indexOf(unit);
  // 自动升级单位，直到值小于阈值或达到最大单位
  while (valueInBaseUnit >= 1000 * factors[units[currentUnitIndex]] && 
        units[currentUnitIndex] !== 'd' && 
        currentUnitIndex < units.length - 1) {
    currentUnitIndex++;
  }
  // 计算转换后的值
  const convertedValue = valueInBaseUnit / factors[units[currentUnitIndex]];
  
  // 格式化结果，保留两位小数
  return `${convertedValue.toFixed(2)} ${units[currentUnitIndex]}`;
}

export function formatterFn (unit){
  switch (unit) {
  case '%':
    return (val) => { return val + '%' }
  case 'bps':
    return (val) => { return transferNetworkUnit(val) }
  case 'Bps':
    return (val) => { return byteTransfer(val, true) + 'ps' }
  case 'MB':
    return (val) => { return byteTransfer(val * 1024 * 1024, true) }
  case 'μs':
    return (val) => { return transferTimeUnit(val, 'μs') }  
  case 'ms':
    return (val) => { return val + 'ms' }
  case 's':
    return (val) => { return val + 's' }
  case 'item':
    return (val) => { return val + i18n.t('monitor.unit.time.ge') }
  case 'IOPS':
    return (val) => { return val + ' IOPS' }
  case 'piece':
    return (val) => { return val + i18n.t('monitor.unit.time') }
  default:
    return (val) => { return val }
  }
}

function dealLargeNum (val){
  val = parseFloat(val)
  if (val > 1000) {
    return val = parseFloat((val / 1000).toFixed(2)) + 'k'
  }
  return parseFloat(val.toFixed(2))
}

export function formatterYAxis (unit){
  switch (unit) {
  case '%':
    return (val) => { return val }
  case 'bps':
    return (val) => { return dealLargeNum(val) }
  case 'Bps':
    return (val) => { return dealLargeNum(val) }
  case 'MB':
    return (val) => { return val }
  case 'ms':
    return (val) => { return dealLargeNum(val.toFixed(2)) }
  case 's':
    return (val) => { return dealLargeNum(val.toFixed(2)) }
  default:
    return (val) => { return dealLargeNum(val) }
  } 
}

export function tooltipFormatterFn (unit){
  return (params) => {
    let htmlStr = ''
    htmlStr += '<div style="margin-bottom:12px;">' + moment(params[0].axisValue).format('YYYY-MM-DD  HH:mm:ss') + '</div>'
    params.forEach(param => {
      htmlStr += '<div style="position:relative;margin-bottom:5px;">'
      htmlStr += '  <div style="display:inline-block;margin-left:6px;"></div>'
      htmlStr += '  <div style="position:absolute;top:50%;margin-left:-5px;margin-top: -5px;width: 8px;height: 8px;border-radius: 50%;background:' + param.color + ';"></div>'
      htmlStr += '<span>'
      const showDataText = (!param.data || (!param.data[1] && param.data[1] !== 0)) ? '-' : formatterFn(unit)(param.data[1])
      htmlStr += param.seriesName + '</span>' + '<span style="float:right;margin:0 0 0 20px;">' + showDataText + '</span>' + '</div>'
    })
    return htmlStr
  }
}

export const serieTemplate = {
  type: 'line',
  smooth: true, // 折线图变平滑
  showSymbol: false, // 去除空心圆点,只有hover的时候才有圆点
  emphasis: {
    lineStyle: {
      width: 2 // hover 后线宽不变，还是2
    }
  },
  data: []
}

/**
 * 返回对象a在对象数组b中与其他项不同的属性对象集合，除去c属性
 * @param {Object} 对象a
 * @param {Array<Object>} 对象数组b
 * @param {Array<String>} 字符串数组c
 */
export function getExtraParam (result, arr, notNeedParams){
  var res = {}
  for(let i = 0;i < arr.length;i++){
    var item = arr[i]?.metric
    Object.keys(item).map(key=>{
      if(notNeedParams.indexOf(key) === -1 && item[key] !== result?.metric?.[key]){
        res[key] = result?.metric?.[key]
      }
    })
  }
  if(Object.keys(res).length > 0){
    return res
  }
  return undefined
}